[gd_scene load_steps=3 format=3 uid="uid://eg5t0jtwwoes"]

[ext_resource type="PackedScene" uid="uid://cxlpyct8223j8" path="res://Scenes/Collectable.tscn" id="1_crystal_base"]
[ext_resource type="Texture2D" uid="uid://bpigwd5ie3c1p" path="res://Assets/kenney_simple-space/AAA-ChosenKenney/meteor_detailedSmall.png" id="2_crystal_tex"]

[node name="CrystalCollectable" instance=ExtResource("1_crystal_base")]
collectable_type = 2
point_value = 50
collection_name = "Crystal"

[node name="Sprite2D" parent="." index="0"]
texture = ExtResource("2_crystal_tex")
modulate = Color(1, 0.5, 1, 1)
scale = Vector2(1.765, 1.765)

[node name="CollectionParticles" parent="." index="2"]
amount = 25
lifetime = 1.2
gravity = Vector2(0, 60)
initial_velocity_min = 70.0
initial_velocity_max = 140.0
color = Color(1, 0, 1, 1)