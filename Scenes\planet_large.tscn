[gd_scene load_steps=5 format=3 uid="uid://d1rrwaam8e5cr"]

[ext_resource type="PackedScene" uid="uid://d0j2xqgs8463m" path="res://Scenes/Planet.tscn" id="1_xtc1m"]
[ext_resource type="Texture2D" uid="uid://b6y5iuktvcwd4" path="res://Assets/kenney_planets/Planets/planet00.png" id="2_qlmj2"]

[sub_resource type="CircleShape2D" id="CircleShape2D_ngwap"]
radius = 2000.0

[sub_resource type="CircleShape2D" id="CircleShape2D_s31nm"]
radius = 832.002

[node name="Planet3" instance=ExtResource("1_xtc1m")]
gravity_strength = 2800.0

[node name="Sprite" parent="." index="0"]
scale = Vector2(1.6, 1.657)
texture = ExtResource("2_qlmj2")

[node name="CollisionShape2D" parent="." index="1"]
shape = SubResource("CircleShape2D_ngwap")

[node name="Sprite2D" parent="CollisionShape2D" index="0"]
scale = Vector2(5.82242, 5.82242)

[node name="CollisionShape2D" parent="AnimatableBody2D" index="0"]
shape = SubResource("CircleShape2D_s31nm")
