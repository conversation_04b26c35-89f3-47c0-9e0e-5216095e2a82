[gd_scene load_steps=12 format=3 uid="uid://14epulnfjodl"]

[ext_resource type="Script" uid="uid://bdfhql41tte7y" path="res://Scripts/HomePlanet.gd" id="1_homeplanet"]
[ext_resource type="Texture2D" uid="uid://cyut047q5pqhb" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceStation_020.png" id="2_station_main"]
[ext_resource type="Texture2D" uid="uid://dwrxcriyojn1o" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceStation_022.png" id="3_station_top"]
[ext_resource type="Texture2D" uid="uid://0l7e77brjgc0" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceStation_018.png" id="4_station_bottom"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_base_collider"]
size = Vector2(173, 49)

[sub_resource type="CircleShape2D" id="CircleShape2D_vtlm0"]
radius = 55.0818

[sub_resource type="RectangleShape2D" id="RectangleShape2D_cxprx"]
size = Vector2(170, 27)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_bpuqj"]
size = Vector2(29, 184)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_xlxvy"]
size = Vector2(8, 60)

[sub_resource type="CircleShape2D" id="CircleShape2D_gravity"]
radius = 400.0

[sub_resource type="RectangleShape2D" id="RectangleShape2D_shop_area"]
size = Vector2(172, 347)

[node name="HomePlanet" type="Area2D"]
script = ExtResource("1_homeplanet")
gravity_strength = 2000.0

[node name="Sprite" type="Sprite2D" parent="."]
texture = ExtResource("2_station_main")

[node name="SpaceStation022" type="Sprite2D" parent="Sprite"]
position = Vector2(0, -84)
texture = ExtResource("3_station_top")

[node name="SpaceStation018" type="Sprite2D" parent="Sprite"]
position = Vector2(0, 59.5)
texture = ExtResource("4_station_bottom")

[node name="AnimatableBody2D" type="AnimatableBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="AnimatableBody2D"]
position = Vector2(-0.5, 60.5)
shape = SubResource("RectangleShape2D_base_collider")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="AnimatableBody2D"]
position = Vector2(0, -119)
shape = SubResource("CircleShape2D_vtlm0")

[node name="CollisionShape2D3" type="CollisionShape2D" parent="AnimatableBody2D"]
position = Vector2(-1, -83.5)
shape = SubResource("RectangleShape2D_cxprx")

[node name="CollisionShape2D4" type="CollisionShape2D" parent="AnimatableBody2D"]
position = Vector2(-0.5, 22)
shape = SubResource("RectangleShape2D_bpuqj")

[node name="CollisionShape2D5" type="CollisionShape2D" parent="AnimatableBody2D"]
position = Vector2(0, 144)
shape = SubResource("RectangleShape2D_xlxvy")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("CircleShape2D_gravity")

[node name="ShopArea" type="Area2D" parent="."]
visible = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="ShopArea"]
position = Vector2(-1, -0.5)
shape = SubResource("RectangleShape2D_shop_area")
