[gd_scene load_steps=15 format=3 uid="uid://cslq0v6w7x80a"]

[ext_resource type="Script" uid="uid://dton838gyavdl" path="res://Scripts/player.gd" id="1_player"]
[ext_resource type="Texture2D" uid="uid://b3yadhtuplhg2" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceShips_001.png" id="2_ship_tex"]
[ext_resource type="Texture2D" uid="uid://b86vrmxx8oexw" path="res://Assets/kenney_simple-space/AAA-ChosenKenney/effect_gray.png" id="3_nn08x"]
[ext_resource type="Script" uid="uid://dwottdnvtkjgy" path="res://Scripts/particle_effect.gd" id="3_particles"]
[ext_resource type="Script" uid="uid://dgc8vmme70xjb" path="res://addons/trail_2d/trail_2d.gd" id="4_52ee3"]
[ext_resource type="Script" uid="uid://vv5wxkdlfjyc" path="res://Scripts/camera_shake.gd" id="4_camera"]
[ext_resource type="Script" uid="uid://deew7a1ghrogw" path="res://Scripts/PlayerAudioHandler.gd" id="5_audio"]

[sub_resource type="CircleShape2D" id="CircleShape2D_kyqiw"]
radius = 40.0

[sub_resource type="Curve" id="Curve_52ee3"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_bhhdu"]
_data = [Vector2(0, 0.502859), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Gradient" id="Gradient_yoq75"]
colors = PackedColorArray(0, 0.643137, 1, 1, 1, 1, 1, 1)

[sub_resource type="Curve" id="Curve_yoq75"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Gradient" id="Gradient_52ee3"]
colors = PackedColorArray(1, 0, 0, 1, 1, 1, 0, 1)

[sub_resource type="Curve" id="Curve_megsn"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[node name="Player" type="RigidBody2D"]
collision_layer = 3
collision_mask = 3
input_pickable = true
gravity_scale = 0.0
freeze_mode = 1
script = ExtResource("1_player")

[node name="Sprite2D" type="Sprite2D" parent="."]
rotation = -1.5708
scale = Vector2(0.454, 0.454)
texture = ExtResource("2_ship_tex")
vframes = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_kyqiw")

[node name="Node2D" type="Node2D" parent="CollisionShape2D"]
position = Vector2(-39, -36)

[node name="Trail2D" type="Line2D" parent="CollisionShape2D/Node2D"]
position = Vector2(-3, 0)
points = PackedVector2Array(5, -1, -88, -1)
width = 13.415
width_curve = SubResource("Curve_52ee3")
texture = ExtResource("3_nn08x")
texture_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_52ee3")

[node name="Node2D2" type="Node2D" parent="CollisionShape2D"]
position = Vector2(-39, 36)

[node name="Trail2D" type="Line2D" parent="CollisionShape2D/Node2D2"]
position = Vector2(-3, 0)
points = PackedVector2Array(5, -1, -88, -1)
width = 13.415
width_curve = SubResource("Curve_52ee3")
texture = ExtResource("3_nn08x")
texture_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_52ee3")

[node name="Line2D" type="Line2D" parent="."]

[node name="BoostParticles-Explosion" type="CPUParticles2D" parent="."]
position = Vector2(-43, 0)
emitting = false
amount = 50
lifetime = 0.6
one_shot = true
preprocess = 0.1
explosiveness = 1.0
local_coords = true
emission_shape = 1
emission_sphere_radius = 10.0
direction = Vector2(0, 0)
spread = 180.0
gravity = Vector2(0, 0)
initial_velocity_max = 100.0
radial_accel_max = 100.0
angle_min = -360.0
angle_max = 360.0
scale_amount_min = 5.0
scale_amount_max = 16.0
scale_amount_curve = SubResource("Curve_bhhdu")
color_ramp = SubResource("Gradient_yoq75")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_particles")

[node name="LaunchParticles" type="CPUParticles2D" parent="."]
position = Vector2(-34, 0)
emitting = false
amount = 25
lifetime = 0.4
one_shot = true
preprocess = 0.1
local_coords = true
emission_shape = 3
emission_rect_extents = Vector2(1, 2.955)
direction = Vector2(0, 0)
spread = 360.0
gravity = Vector2(-980, 0)
angle_min = -360.0
angle_max = 360.0
scale_amount_max = 10.0
scale_amount_curve = SubResource("Curve_yoq75")
color_ramp = SubResource("Gradient_52ee3")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_particles")

[node name="BoostParticles" type="CPUParticles2D" parent="."]
position = Vector2(-43, 0)
emitting = false
amount = 25
lifetime = 0.5
one_shot = true
preprocess = 0.1
local_coords = true
emission_shape = 3
emission_rect_extents = Vector2(1, 2.955)
direction = Vector2(0, 0)
spread = 360.0
gravity = Vector2(-980, 0)
initial_velocity_min = 2.0
initial_velocity_max = 15.0
angle_min = -360.0
angle_max = 360.0
scale_amount_min = 2.0
scale_amount_max = 15.0
scale_amount_curve = SubResource("Curve_megsn")
color_ramp = SubResource("Gradient_yoq75")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_particles")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(0.345, 0.345)
script = ExtResource("4_camera")

[node name="AudioHandler" type="Node2D" parent="."]
script = ExtResource("5_audio")

[node name="AudioListener2D" type="AudioListener2D" parent="."]
