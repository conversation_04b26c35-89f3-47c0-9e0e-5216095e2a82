[gd_scene load_steps=7 format=3 uid="uid://bwo0604aug3qx"]

[ext_resource type="Texture2D" uid="uid://dpxblr1n1y0y4" path="res://Assets/UI/SlideTransition.png" id="1_363dy"]
[ext_resource type="Script" uid="uid://ktoh6av20xgx" path="res://Scripts/slide_transition.gd" id="1_elk8h"]

[sub_resource type="Animation" id="Animation_elk8h"]
resource_name = "RESET"
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("TextureRect:position:x")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(-2161, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("TextureRect:position:y")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="Animation" id="Animation_u0eb1"]
resource_name = "SlidePart1"
length = 0.5
markers = [{
"color": Color(1, 1, 1, 1),
"name": &"new_marker",
"time": 0.5
}]
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("TextureRect:position:x")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(-2161, -0.25, 0, 0.25, 0, -514, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 0.5)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/path = NodePath("TextureRect:position:y")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 0.5)
}

[sub_resource type="Animation" id="Animation_2pji4"]
resource_name = "SlidePart2"
length = 0.5
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("TextureRect:position:x")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0, 1152, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 0.5)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("TextureRect:position:y")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 0.5)
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_2pji4"]
_data = {
&"RESET": SubResource("Animation_elk8h"),
&"SlidePart1": SubResource("Animation_u0eb1"),
&"SlidePart2": SubResource("Animation_2pji4")
}

[node name="CanvasLayer" type="CanvasLayer"]
script = ExtResource("1_elk8h")

[node name="TextureRect" type="TextureRect" parent="."]
offset_left = -2161.0
offset_right = -1.0
offset_bottom = 650.0
texture = ExtResource("1_363dy")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_2pji4")
}
