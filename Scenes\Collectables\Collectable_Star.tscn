[gd_scene load_steps=3 format=3 uid="uid://c8x7y2qm8qrst"]

[ext_resource type="PackedScene" uid="uid://cxlpyct8223j8" path="res://Scenes/Collectable.tscn" id="1_starbase"]
[ext_resource type="Texture2D" uid="uid://ba4pw6xs2250w" path="res://Assets/kenney_simple-space/AAA-ChosenKenney/star_medium.png" id="2_startex"]

[node name="StarCollectable" instance=ExtResource("1_starbase")]
point_value = 10
collection_name = "Star"

[node name="Sprite2D" parent="." index="0"]
scale = Vector2(1.925, 1.925)
texture = ExtResource("2_startex")

[node name="CollectionParticles" parent="." index="2"]
amount = 15
lifetime = 0.8
gravity = Vector2(0, 50)
initial_velocity_min = 40.0
initial_velocity_max = 80.0
scale_amount_min = 0.3
color = Color(1, 1, 0, 1)
