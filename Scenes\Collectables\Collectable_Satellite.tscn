[gd_scene load_steps=3 format=3 uid="uid://c7t01e3r3o2i"]

[ext_resource type="PackedScene" uid="uid://cxlpyct8223j8" path="res://Scenes/Collectable.tscn" id="1_satellite_base"]
[ext_resource type="Texture2D" uid="uid://dwrxcriyojn1o" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceStation_022.png" id="2_satellite_tex"]

[node name="SatelliteCollectable" instance=ExtResource("1_satellite_base")]
collectable_type = 1
point_value = 25
collection_name = "Satellite"

[node name="Sprite2D" parent="." index="0"]
texture = ExtResource("2_satellite_tex")
modulate = Color(0.8, 1, 1, 1)

[node name="CollectionParticles" parent="." index="2"]
gravity = Vector2(0, 80)
initial_velocity_min = 60.0
initial_velocity_max = 120.0
scale_amount_min = 0.4
scale_amount_max = 1.2
color = Color(0, 1, 1, 1)