[gd_scene load_steps=3 format=3 uid="uid://bficc510f248i"]

[ext_resource type="PackedScene" uid="uid://cxlpyct8223j8" path="res://Scenes/Collectable.tscn" id="1_energy_base"]
[ext_resource type="Texture2D" uid="uid://cx06de664828k" path="res://Assets/kenney_space-shooter-extension/AAA-ChosenKenneySpace/spaceBuilding_002.png" id="2_energy_tex"]

[node name="EnergyCoreCollectable" instance=ExtResource("1_energy_base")]
collectable_type = 3
collection_name = "Energy Core"

[node name="Sprite2D" parent="." index="0"]
texture = ExtResource("2_energy_tex")

[node name="CollectionParticles" parent="." index="2"]
amount = 30
lifetime = 1.5
gravity = Vector2(0, 40)
initial_velocity_min = 80.0
initial_velocity_max = 160.0
scale_amount_min = 0.6
scale_amount_max = 2.0
color = Color(0, 1, 0, 1)